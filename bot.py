import time
import pandas as pd
import os
from logging.handlers import RotatingFileHandler
from typing import Dict, <PERSON><PERSON>, Optional
from datetime import datetime, timedelta, timezone
import random
import discord_handler

# <PERSON>h<PERSON><PERSON> gian bắt đầu bot
bot_start_time = datetime.now(timezone.utc)

try:
    from talib import ATR
except ImportError:
    print("TA-Lib not found. Please install it: https://mrjbq7.github.io/ta-lib/install.html")
    exit(1)

from contract_v1_python_demo import get_kline, get_account_asset_currency, get_open_positions
import yaml
import symbol
import trading_strategy as strategy
import logging
import importlib
from basic_call import place_order
import ibsymbol
import threading

LOG_DIR = "logs"
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

def setup_logging():
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    formatter = logging.Formatter(log_format)

    # Console handler for immediate feedback
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)

    trade_logger = logging.getLogger('trade')
    trade_logger.setLevel(logging.INFO)
    trade_handler = RotatingFileHandler(os.path.join(LOG_DIR, 'trade.log'), maxBytes=10*1024*1024, backupCount=5)
    trade_handler.setFormatter(formatter)
    trade_logger.addHandler(trade_handler)
    trade_logger.addHandler(console_handler)

    logic_logger = logging.getLogger('logic')
    logic_logger.setLevel(logging.DEBUG)
    logic_handler = RotatingFileHandler(os.path.join(LOG_DIR, 'logic.log'), maxBytes=10*1024*1024, backupCount=5)
    logic_handler.setFormatter(formatter)
    logic_logger.addHandler(logic_handler)

    error_logger = logging.getLogger('error')
    error_logger.setLevel(logging.ERROR)
    error_handler = RotatingFileHandler(os.path.join(LOG_DIR, 'error.log'), maxBytes=10*1024*1024, backupCount=5)
    error_handler.setFormatter(formatter)
    error_logger.addHandler(error_handler)
    error_logger.addHandler(console_handler)

    # Discord logger
    discord_logger = logging.getLogger('discord')
    discord_logger.setLevel(logging.INFO)
    discord_handler = RotatingFileHandler(os.path.join(LOG_DIR, 'discord.log'), maxBytes=10*1024*1024, backupCount=5)
    discord_handler.setFormatter(formatter)
    discord_logger.addHandler(discord_handler)
    discord_logger.addHandler(console_handler)

    return trade_logger, logic_logger, error_logger

trade_logger, logic_logger, error_logger = setup_logging()

# --- Load cấu hình từ config.yaml ---
with open("config.yaml", "r") as file:
    config = yaml.safe_load(file)
DISCORD_TOKEN = config["discord"]["token"]
DISCORD_ADMIN_ID = config["discord"]["admin_id"]
DISCORD_GUILD_ID = config["discord"]["guild_id"]
DISCORD_CHANNEL = config["discord"]["channel"]
BASE_USDT = float(config["BASE_USDT"])
BASE_LEVERAGE = int(config["BASE_LEVERAGE"])
RR_RATIO = float(config["RR_RATIO"])
SLEEP_INTERVAL = int(config["SLEEP_INTERVAL"])
WATCHLIST_RETENTION_MINUTES = int(config["WATCHLIST_RETENTION_MINUTES"])
ATR_TIMEFRAME = config["ATR_TIMEFRAME"]
ATR_PERIOD = int(config["ATR_PERIOD"])
ATR_SL_MULTIPLIER = float(config["ATR_SL_MULTIPLIER"])
MIN_SL_PERCENTAGE = float(config["MIN_SL_PERCENTAGE"])
ORDER_WARN_DURATION_H1 = int(config["ORDER_WARN_DURATION_H1"])
ORDER_WARN_DURATION_H2 = int(config["ORDER_WARN_DURATION_H2"])
ORDER_SUMMARY_INTERVAL_MIN = int(config["ORDER_SUMMARY_INTERVAL_MIN"])
RSI_THRESHOLD = float(config.get("RSI_THRESHOLD", 21.0))
M1_VOLUME_AVG_PERIOD = int(config.get("M1_VOLUME_AVG_PERIOD", 10))
ENTRY_CANDLE_TOLERANCE_PCT = float(config.get("ENTRY_CANDLE_TOLERANCE_PCT", 0.1))
ENTRY_ATR_AVG_PERIOD = int(config.get("ENTRY_ATR_AVG_PERIOD", 5))
ENTRY_VOL_AVG_PERIOD = int(config.get("ENTRY_VOL_AVG_PERIOD", 5))
MIN_WATCHLIST_DURATION_MIN = int(config["MIN_WATCHLIST_DURATION_MIN"])
MAX_PRICE_DEVIATION_PERCENT = float(config["MAX_PRICE_DEVIATION_PERCENT"])
MEXC_SYMBOL = symbol.MEXC_SYMBOL
VOLUME_FACTOR = symbol.VOLUME_FACTOR
SYMBOL_FACTOR_MAP = dict(zip(MEXC_SYMBOL, VOLUME_FACTOR))

# --- Biến toàn cục ---
open_orders: Dict[str, dict] = {}
excluded_symbols: Dict[str, datetime] = {}
EXCLUSION_DURATION_MIN = 5
current_balance = 0.0
last_summary_time = datetime.now(timezone.utc) - timedelta(minutes=ORDER_SUMMARY_INTERVAL_MIN + 1)

# --- Lớp TradingBot chính ---
class TradingBot:
    def __init__(self):
        self.watch_list: Dict[str, dict] = {}
        self.order_warnings_sent: Dict[str, Dict[int, bool]] = {}

        # Cải thiện cơ chế cache
        self.kline_cache: Dict[Tuple[str, str], Tuple[pd.DataFrame, datetime]] = {}
        self.price_cache: Dict[str, Tuple[float, float]] = {}
        self.max_cache_size = 1000

        # Cấu hình TTL cache theo khung thời gian
        self.cache_ttl = {
            'Min1': 30,      # 30 giây cho M1
            'Min5': 60,      # 60 giây cho M5
            'Min15': 120,    # 120 giây cho M15
            'Min30': 300,    # 5 phút cho M30
            'Min60': 600,    # 10 phút cho H1
            'Hour4': 1800,   # 30 phút cho H4
            'Hour8': 3600,   # 1 giờ cho H8
            'Day1': 7200,    # 2 giờ cho D1
            'default': 60    # Mặc định 60 giây
        }

        # Khởi tạo DiscordHandler
        try:
            logic_logger.info("Đang khởi tạo Discord handler...")
            self.discord = discord_handler.DiscordHandler(DISCORD_TOKEN, DISCORD_ADMIN_ID, DISCORD_GUILD_ID, DISCORD_CHANNEL)

            # Đăng ký trading commands
            callbacks = {
                'status': self.get_bot_status_info,
                'balance': self.get_balance_info,
                'stats': self.get_trading_stats,
                'dashboard': self.handle_dashboard_command,
                'orders': self.get_open_orders_info,
                'pause': self.handle_pause_bot,
                'resume': self.handle_resume_bot,
            }
            self.discord.register_trading_commands(callbacks)

            logic_logger.info("Đang bắt đầu Discord polling...")
            self.discord.start_polling()
            logic_logger.info("Discord handler đã được khởi tạo thành công")
        except Exception as e:
            error_logger.error(f"Lỗi khởi tạo Discord handler: {e}")
            raise

        # Dashboard Manager
        self.dashboard_message_id = None
        self.dashboard_thread = None
        self.dashboard_stop_event = threading.Event()



    def handle_update_symbols(self, _user_id: str, args: str, _message) -> str:
        """Xử lý lệnh cập nhật danh sách symbol từ Discord"""
        # Phân tích tham số số lượng symbol
        max_symbols = 20  # Giá trị mặc định

        if args and args.strip():
            try:
                # Thử chuyển đổi tham số thành số nguyên
                input_max_symbols = int(args.strip())
                if 5 <= input_max_symbols <= 100:
                    max_symbols = input_max_symbols
                else:
                    return "⚠️ Số lượng symbol phải từ 5 đến 100. Sử dụng giá trị mặc định (20)."
            except ValueError:
                return "⚠️ Tham số không hợp lệ. Sử dụng giá trị mặc định (20)."

        try:
            # Gọi hàm cập nhật symbol từ ibsymbol.py với số lượng symbol được chỉ định
            result = ibsymbol.update_symbols(max_symbols=max_symbols)

            if result["success"]:
                # Nếu cập nhật thành công, tải lại các biến toàn cục
                self.reload_symbols()

                # Tạo thông báo thành công với danh sách symbol
                display_count = min(10, len(result["symbols"]))
                symbols_list = "\n".join([f"- {sym}" for sym in result["symbols"][:display_count]])
                if len(result["symbols"]) > display_count:
                    symbols_list += f"\n... và {len(result['symbols']) - display_count} symbol khác"

                success_msg = (
                    f"✅ **Cập nhật symbol thành công!**\n"
                    f"⏰ Thời gian: {result['timestamp']}\n"
                    f"📊 Số lượng: {result['symbols_count']} symbol\n\n"
                    f"**Top symbols:**\n{symbols_list}"
                )
                return success_msg
            else:
                # Thông báo lỗi nếu cập nhật thất bại
                return f"❌ **Cập nhật symbol thất bại:**\n{result['message']}"
        except Exception as e:
            error_logger.error(f"Lỗi khi cập nhật symbol: {e}")
            return f"❌ **Lỗi khi cập nhật symbol:**\n{str(e)}"

    def reload_symbols(self) -> None:
        """Tải lại module symbol và cập nhật các biến toàn cục liên quan"""
        global MEXC_SYMBOL, VOLUME_FACTOR, SYMBOL_FACTOR_MAP

        try:
            # Tải lại module symbol
            importlib.reload(symbol)

            # Cập nhật các biến toàn cục
            MEXC_SYMBOL = symbol.MEXC_SYMBOL
            VOLUME_FACTOR = symbol.VOLUME_FACTOR
            SYMBOL_FACTOR_MAP = dict(zip(MEXC_SYMBOL, VOLUME_FACTOR))

            logic_logger.info(f"Đã tải lại module symbol: {len(MEXC_SYMBOL)} symbols")
            return True
        except Exception as e:
            error_logger.error(f"Lỗi khi tải lại module symbol: {e}")
            return False

    def send_discord_message(self, message: str) -> None:
        """Gửi tin nhắn qua Discord sử dụng handler"""
        self.discord.send_message_sync(message)

    def get_real_account_data(self) -> dict:
        """Lấy dữ liệu tài khoản thực từ API"""
        try:
            result, error = get_account_asset_currency('USDT')
            if error:
                logic_logger.error(f"Lỗi lấy dữ liệu tài khoản: {error}")
                return {
                    'total_balance': 0.0,
                    'free_balance': 0.0,
                    'available': 0.0,
                    'error': error
                }

            if result and result.get('success', True):
                data = result.get('data', {})
                return {
                    'total_balance': float(data.get('equity', 0)),
                    'free_balance': float(data.get('availableBalance', 0)),
                    'available': float(data.get('availableOpen', 0)),
                    'error': None
                }
            else:
                return {
                    'total_balance': 0.0,
                    'free_balance': 0.0,
                    'available': 0.0,
                    'error': result.get('message', 'Unknown error')
                }
        except Exception as e:
            logic_logger.error(f"Exception khi lấy dữ liệu tài khoản: {e}")
            return {
                'total_balance': 0.0,
                'free_balance': 0.0,
                'available': 0.0,
                'error': str(e)
            }

    def get_real_positions_data(self) -> dict:
        """Lấy dữ liệu positions thực từ API"""
        try:
            result = get_open_positions()
            if not isinstance(result, dict):
                return {
                    'positions': [],
                    'total_unrealized_pnl': 0.0,
                    'open_positions_count': 0,
                    'error': 'Invalid API response format'
                }

            if not result.get('success', True):
                return {
                    'positions': [],
                    'total_unrealized_pnl': 0.0,
                    'open_positions_count': 0,
                    'error': result.get('message', 'API error')
                }

            positions = result.get('data', [])
            total_unrealized_pnl = 0.0

            for pos in positions:
                pnl = pos.get('unrealizedPnl', 0)
                if isinstance(pnl, (int, float)):
                    total_unrealized_pnl += float(pnl)
                elif isinstance(pnl, str):
                    try:
                        total_unrealized_pnl += float(pnl)
                    except ValueError:
                        pass

            return {
                'positions': positions,
                'total_unrealized_pnl': total_unrealized_pnl,
                'open_positions_count': len(positions),
                'error': None
            }
        except Exception as e:
            logic_logger.error(f"Exception khi lấy dữ liệu positions: {e}")
            return {
                'positions': [],
                'total_unrealized_pnl': 0.0,
                'open_positions_count': 0,
                'error': str(e)
            }

    def create_dashboard_message(self) -> str:
        """Tạo dashboard message với dữ liệu thực từ API"""
        now_utc = datetime.now(timezone.utc)

        # Lấy dữ liệu thực từ API
        account_data = self.get_real_account_data()
        positions_data = self.get_real_positions_data()

        # Tính uptime
        uptime = now_utc - bot_start_time
        total_seconds = int(uptime.total_seconds())
        hours, rem = divmod(total_seconds, 3600)
        minutes, seconds = divmod(rem, 60)
        uptime_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

        # Tạo dashboard message
        dashboard_msg = "📊 **Trading Status Dashboard**\n"
        dashboard_msg += f"⏰ Last Update: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
        dashboard_msg += f"🤖 Uptime: {uptime_str}\n\n"

        # Account Status
        dashboard_msg += "💰 **Account Status**\n"
        if account_data['error']:
            dashboard_msg += f"❌ Error: {account_data['error']}\n"
        else:
            dashboard_msg += f"Total Balance: ${account_data['total_balance']:.2f}\n"
            dashboard_msg += f"Free Balance: ${account_data['free_balance']:.2f}\n"
            dashboard_msg += f"Available: ${account_data['available']:.2f}\n"

        dashboard_msg += "\n📈 **P&L Summary**\n"
        if positions_data['error']:
            dashboard_msg += f"❌ Error: {positions_data['error']}\n"
        else:
            pnl_icon = "🟢" if positions_data['total_unrealized_pnl'] >= 0 else "🔴"
            dashboard_msg += f"Unrealized P&L: {pnl_icon} ${positions_data['total_unrealized_pnl']:.2f}\n"
            dashboard_msg += f"Open Positions: {positions_data['open_positions_count']}\n"
            dashboard_msg += f"Pending Orders: 0\n"  # TODO: Implement pending orders count

        # Open Positions Details
        dashboard_msg += "\n📊 **Open Positions**\n"
        if positions_data['error']:
            dashboard_msg += f"❌ Error loading positions\n"
        elif not positions_data['positions']:
            dashboard_msg += "No open positions\n"
        else:
            for pos in positions_data['positions'][:5]:  # Hiển thị tối đa 5 positions
                symbol = pos.get('symbol', 'N/A')
                side = pos.get('positionType', 'N/A')
                entry_price = pos.get('openPrice', 0)
                mark_price = pos.get('markPrice', 0)
                unrealized_pnl = pos.get('unrealizedPnl', 0)

                # Tính phần trăm P&L
                pnl_percent = 0
                if entry_price and mark_price:
                    try:
                        entry_price = float(entry_price)
                        mark_price = float(mark_price)
                        if side == 'LONG':
                            pnl_percent = ((mark_price - entry_price) / entry_price) * 100
                        else:
                            pnl_percent = ((entry_price - mark_price) / entry_price) * 100
                    except (ValueError, ZeroDivisionError):
                        pnl_percent = 0

                pnl_icon = "🟢" if float(unrealized_pnl) >= 0 else "🔴"
                pnl_percent_icon = "🟢" if pnl_percent >= 0 else "🔴"

                dashboard_msg += f"{symbol} {side} {pnl_icon} ${unrealized_pnl} ({pnl_percent_icon}{pnl_percent:.2f}%)\n"
                dashboard_msg += f"Entry: ${entry_price} | Mark: ${mark_price}\n"

            if len(positions_data['positions']) > 5:
                dashboard_msg += f"... và {len(positions_data['positions']) - 5} positions khác\n"

        return dashboard_msg

    def start_dashboard(self) -> None:
        """Khởi động dashboard tự động cập nhật"""
        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                # Đảm bảo Discord bot sẵn sàng
                if not self.discord.wait_for_ready(timeout=10):
                    error_logger.warning(f"Discord bot chưa sẵn sàng, thử lại lần {attempt + 1}")
                    time.sleep(retry_delay)
                    continue

                # Gửi dashboard message đầu tiên và lưu message ID
                initial_message = self.create_dashboard_message()
                self.dashboard_message_id = self.discord.send_message_and_get_id_sync(initial_message)

                if self.dashboard_message_id:
                    logic_logger.info(f"Dashboard đã được tạo với message ID: {self.dashboard_message_id}")

                    # Bắt đầu thread cập nhật dashboard
                    self.dashboard_thread = threading.Thread(target=self._dashboard_update_loop, daemon=True)
                    self.dashboard_thread.start()
                    logic_logger.info("Dashboard auto-update thread đã được khởi động")
                    return
                else:
                    error_logger.warning(f"Không thể tạo dashboard message, thử lại lần {attempt + 1}")

            except Exception as e:
                error_logger.error(f"Lỗi khởi động dashboard (lần {attempt + 1}): {e}")

            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                retry_delay *= 2

        error_logger.error("Không thể khởi động dashboard sau nhiều lần thử")

    def _dashboard_update_loop(self) -> None:
        """Vòng lặp cập nhật dashboard mỗi 60 giây"""
        consecutive_failures = 0
        max_failures = 5

        while not self.dashboard_stop_event.is_set():
            try:
                # Chờ 60 giây hoặc cho đến khi stop event được set
                if self.dashboard_stop_event.wait(60):
                    break

                # Kiểm tra Discord bot vẫn sẵn sàng
                if not self.discord.bot.is_ready():
                    logic_logger.warning("Discord bot không sẵn sàng, bỏ qua cập nhật dashboard")
                    consecutive_failures += 1
                    if consecutive_failures >= max_failures:
                        error_logger.error("Quá nhiều lỗi liên tiếp, dừng dashboard update")
                        break
                    continue

                # Cập nhật dashboard
                if self.dashboard_message_id:
                    updated_message = self.create_dashboard_message()
                    success = self.discord.edit_message_sync(self.dashboard_message_id, updated_message)
                    if success:
                        logic_logger.debug("Dashboard đã được cập nhật thành công")
                        consecutive_failures = 0  # Reset counter khi thành công
                    else:
                        logic_logger.warning("Không thể cập nhật dashboard")
                        consecutive_failures += 1
                        if consecutive_failures >= max_failures:
                            error_logger.error("Quá nhiều lỗi liên tiếp, dừng dashboard update")
                            break

            except Exception as e:
                error_logger.error(f"Lỗi trong dashboard update loop: {e}")
                consecutive_failures += 1
                if consecutive_failures >= max_failures:
                    error_logger.error("Quá nhiều lỗi liên tiếp, dừng dashboard update")
                    break
                # Tiếp tục vòng lặp ngay cả khi có lỗi
                time.sleep(5)

    def stop_dashboard(self) -> None:
        """Dừng dashboard auto-update"""
        try:
            if self.dashboard_stop_event:
                self.dashboard_stop_event.set()

            if self.dashboard_thread and self.dashboard_thread.is_alive():
                self.dashboard_thread.join(timeout=5)
                logic_logger.info("Dashboard thread đã được dừng")
        except Exception as e:
            error_logger.error(f"Lỗi khi dừng dashboard: {e}")

    def get_bot_status_info(self) -> str:
        """Tạo thông tin trạng thái bot"""
        now_utc = datetime.now(timezone.utc)
        total_equity = self.calculate_total_equity()

        # Tính uptime
        uptime = now_utc - bot_start_time
        total_seconds = int(uptime.total_seconds())
        hours, rem = divmod(total_seconds, 3600)
        minutes, seconds = divmod(rem, 60)
        uptime_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

        # Trạng thái bot
        bot_status = "🛑 TẠM DỪNG" if self.is_paused else "✅ ĐANG CHẠY"

        status_msg = (
            f"**📊 #BOT_STATUS**\n"
            f"⏰ Thời gian: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
            f"🤖 Uptime: {uptime_str}\n"
            f"🚦 Trạng thái: {bot_status}\n\n"
            f"**Tài khoản:**\n"
            f"🏦 Số dư: {current_balance:.2f} USDT\n"
            f"💰 Unrealized PNL: {(total_equity - current_balance):.2f} USDT\n"
            f"📈 Total Equity: {total_equity:.2f} USDT\n\n"
            f"**Giao dịch:**\n"
            f"📈 Lệnh đang mở: {len(open_orders)}\n"
            f"👀 Watchlist: {len(self.watch_list)} cặp\n"
            f"❌ Excluded: {len(excluded_symbols)} cặp\n\n"
            f"**Cấu hình:**\n"
            f"⚙️ RR={RR_RATIO}, ATR SL={ATR_SL_MULTIPLIER}x({ATR_TIMEFRAME})\n"
            f"💵 Kích thước lệnh: {BASE_USDT:.2f} USDT\n"
            f"📊 Đòn bẩy: {BASE_LEVERAGE}x"
        )
        return status_msg

    def get_balance_info(self) -> str:
        """Tạo thông tin chi tiết về số dư và equity"""
        total_equity = self.calculate_total_equity()
        unrealized_pnl = total_equity - current_balance

        balance_msg = (
            f"**💰 #BALANCE_INFO**\n"
            f"🏦 Số dư hiện tại: **{current_balance:.2f} USDT**\n"
            f"💰 Unrealized PNL: {unrealized_pnl:.2f} USDT\n"
            f"📈 Total Equity: {total_equity:.2f} USDT\n"
            f"⚙️ Đòn bẩy: {BASE_LEVERAGE}x\n"
            f"💲 Kích thước lệnh: {BASE_USDT:.2f} USDT"
        )
        return balance_msg

    def get_open_orders_info(self) -> str:
        """Tạo thông tin về các lệnh đang mở"""
        now_utc = datetime.now(timezone.utc)
        unrealized_pnl_total = 0.0
        open_orders_summary = []
        for sym, order in open_orders.items():
            current_price = self.get_current_price(sym)
            if current_price is None:
                continue
            entry = order["entry"]
            volume = order["volume"]
            factor = SYMBOL_FACTOR_MAP.get(sym, 1.0)
            side = order["side"]
            precision = order.get('precision', 2)
            duration = now_utc - order['open_time']
            total_seconds = int(duration.total_seconds())
            hours, rem = divmod(total_seconds, 3600)
            minutes, seconds = divmod(rem, 60)
            duration_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
            if side == "LONG":
                pnl = (current_price - entry) * volume * factor
            else:
                pnl = (entry - current_price) * volume * factor
            unrealized_pnl_total += pnl
            pnl_percent = (pnl / (entry * volume * factor / BASE_LEVERAGE)) * 100
            symbol_base = sym.split('_')[0]
            open_orders_summary.append(
                f"🔹 #{symbol_base} ({side})\n"
                f"   Entry: {entry:.{precision}f} | Current: {current_price:.{precision}f}\n"
                f"   PNL: {pnl:.2f} USDT ({pnl_percent:.2f}%)\n"
                f"   TP: {order['tp']:.{precision}f} | SL: {order['sl']:.{precision}f}\n"
                f"   Thời gian: {duration_str}"
            )
        if not open_orders:
            orders_msg = (
                f"**📊 #ORDERS_INFO**\n"
                f"⏰ Thời gian: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
                f"📈 Lệnh đang mở: 0\n\n"
                f"Hiện không có lệnh nào đang mở."
            )
        else:
            orders_msg = (
                f"**📊 #ORDERS_INFO**\n"
                f"⏰ Thời gian: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
                f"📈 Lệnh đang mở: {len(open_orders)}\n"
                f"💰 Tổng Unrealized PNL: {unrealized_pnl_total:.2f} USDT\n\n"
            ) + "\n".join(open_orders_summary)
        return orders_msg

    def get_open_positions_info(self) -> str:
        """Lấy thông tin open positions từ API, format cho Discord"""
        from contract_v1_python_demo import get_open_positions
        now_utc = datetime.now(timezone.utc)
        try:
            res = get_open_positions()
            positions = res.get('data', []) if isinstance(res, dict) else []
        except Exception as e:
            return f"**🆘 LỖI lấy open positions:**\n```{str(e)[:300]}```"
        if not positions:
            return (
                f"**📊 #OPEN_POSITIONS**\n"
                f"⏰ Thời gian: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
                f"📈 Không có vị thế mở."
            )
        msg_lines = [
            f"**📊 #OPEN_POSITIONS**",
            f"⏰ Thời gian: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
        ]
        for pos in positions:
            symbol = pos.get('symbol', '-')
            side = pos.get('positionType', '-')
            entry = pos.get('openPrice', '-')
            vol = pos.get('vol', '-')
            leverage = pos.get('leverage', '-')
            pnl = pos.get('unrealizedPnl', '-')
            liq = pos.get('liquidatePrice', '-')
            msg_lines.append(
                f"🔸 **{symbol}** ({side})\n"
                f"   Entry: {entry} | Vol: {vol} | Leverage: {leverage}x\n"
                f"   PNL: {pnl} USDT | Liq: {liq}"
            )
        return "\n".join(msg_lines)

    # --- Các phương thức xử lý lệnh mới ---

    def get_trading_stats(self) -> str:
        """Tạo thống kê giao dịch"""
        now_utc = datetime.now(timezone.utc)

        # TODO: Thêm logic tính toán thống kê thực tế từ lịch sử giao dịch
        # Hiện tại chỉ hiển thị thông tin mẫu

        total_trades = 0
        winning_trades = 0
        losing_trades = 0

        # Tính tỷ lệ thắng/thua
        win_rate = 0
        if total_trades > 0:
            win_rate = (winning_trades / total_trades) * 100

        # Tính PnL trung bình
        avg_pnl = 0
        if total_trades > 0:
            avg_pnl = current_balance / total_trades

        stats_msg = (
            f"**📊 #TRADING_STATS**\n"
            f"⏰ Thời gian: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n\n"
            f"**Tổng quan:**\n"
            f"🔢 Tổng số lệnh: {total_trades}\n"
            f"✅ Lệnh thắng: {winning_trades}\n"
            f"❌ Lệnh thua: {losing_trades}\n"
            f"📈 Tỷ lệ thắng: {win_rate:.2f}%\n\n"
            f"**Lợi nhuận:**\n"
            f"💰 Tổng P&L: {current_balance:.2f} USDT\n"
            f"📊 P&L trung bình: {avg_pnl:.2f} USDT/lệnh\n"
            f"💵 Số dư hiện tại: {current_balance:.2f} USDT"
        )

        return stats_msg

    def handle_settings(self, args: str) -> str:
        """Xử lý xem/thay đổi cài đặt"""
        now_utc = datetime.now(timezone.utc)

        # Nếu không có tham số, hiển thị cài đặt hiện tại
        if not args or not args.strip():
            settings_msg = (
                f"**⚙️ #BOT_SETTINGS**\n"
                f"⏰ Thời gian: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n\n"
                f"**Cài đặt giao dịch:**\n"
                f"💵 Kích thước lệnh: {BASE_USDT:.2f} USDT\n"
                f"📊 Đòn bẩy: {BASE_LEVERAGE}x\n"
                f"📈 Tỷ lệ R/R: {RR_RATIO}\n"
                f"📉 ATR SL: {ATR_SL_MULTIPLIER}x ({ATR_TIMEFRAME})\n\n"
                f"**Cài đặt RSI:**\n"
                f"📊 Ngưỡng RSI: {RSI_THRESHOLD}\n"
                f"📊 Chu kỳ volume M1: {M1_VOLUME_AVG_PERIOD}\n\n"
                f"**Cài đặt hệ thống:**\n"
                f"⏱️ Chu kỳ quét: {SLEEP_INTERVAL}s\n"
                f"⏱️ Thời gian giữ watchlist: {WATCHLIST_RETENTION_MINUTES}m\n"
                f"⏱️ Thời gian loại trừ symbol: {EXCLUSION_DURATION_MIN}m\n\n"
                f"*Để thay đổi cài đặt, sử dụng lệnh:*\n"
                f"`/settings [tham_số] [giá_trị]`\n\n"
                f"*Ví dụ:*\n"
                f"`/settings BASE_USDT 0.2`"
            )
            return settings_msg

        # TODO: Thêm logic thay đổi cài đặt
        # Hiện tại chỉ hiển thị thông báo
        return "⚠️ Chức năng thay đổi cài đặt đang được phát triển."

    def handle_clear_symbol(self, symbol: str) -> str:
        """Xử lý xóa symbol khỏi danh sách loại trừ"""
        global excluded_symbols

        # Kiểm tra xem symbol có trong danh sách loại trừ không
        if symbol not in excluded_symbols:
            return f"⚠️ Symbol {symbol} không có trong danh sách loại trừ."

        # Xóa symbol khỏi danh sách loại trừ
        del excluded_symbols[symbol]
        logic_logger.info(f"Đã xóa {symbol} khỏi danh sách loại trừ theo yêu cầu")

        return f"✅ Đã xóa {symbol} khỏi danh sách loại trừ thành công."

    # Biến trạng thái bot
    is_paused = False

    def handle_pause_bot(self) -> str:
        """Tạm dừng bot"""
        self.is_paused = True
        logic_logger.info("Bot đã được tạm dừng theo yêu cầu")

        return "🛑 Bot đã được tạm dừng. Sẽ không đặt lệnh mới cho đến khi được tiếp tục."

    def handle_resume_bot(self) -> str:
        """Tiếp tục bot"""
        self.is_paused = False
        logic_logger.info("Bot đã được tiếp tục theo yêu cầu")

        return "✅ Bot đã được tiếp tục. Đang quét thị trường và sẵn sàng đặt lệnh."

    def get_watchlist_info(self) -> str:
        """Tạo thông tin về watchlist"""
        now_utc = datetime.now(timezone.utc)
        watchlist_summary = []

        if not self.watch_list:
            return (
                f"**👀 #WATCHLIST_INFO**\n"
                f"⏰ Thời gian: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
                f"📈 Cặp theo dõi: 0\n\n"
                f"Hiện không có cặp nào trong watchlist."
            )

        for sym, data in self.watch_list.items():
            side = data['side']
            added_time = data['added_time']
            time_in_watchlist = (now_utc - added_time).total_seconds() / 60
            price_at_add = data['price_at_add']
            rsi_at_add = data.get('m1_rsi_at_add', 'N/A')

            current_price = None
            price_change = "N/A"
            try:
                df_m1 = self.get_kline_data(sym, "Min1", limit=5)
                if df_m1 is not None and not df_m1.empty:
                    current_price = df_m1['close'].iloc[-1]
                    price_change = f"{((current_price - price_at_add) / price_at_add * 100):.2f}%"
            except Exception:
                pass

            symbol_base = sym.split('_')[0]
            watchlist_summary.append(
                f"👁️ #{symbol_base} ({side})\n"
                f"   Thêm: {time_in_watchlist:.1f} phút trước\n"
                f"   RSI (M1): {rsi_at_add if isinstance(rsi_at_add, float) else 'N/A'}\n"
                f"   Giá thêm: {price_at_add:.6f}\n"
                f"   Thay đổi: {price_change}"
            )

        watchlist_msg = (
            f"**👀 #WATCHLIST_INFO**\n"
            f"⏰ Thời gian: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
            f"📈 Cặp theo dõi: {len(self.watch_list)}\n\n"
        ) + "\n\n".join(watchlist_summary)

        return watchlist_msg

    def get_current_price(self, sym: str) -> Optional[float]:
        """Lấy giá hiện tại của symbol với cơ chế cache cải tiến"""
        now = time.time()
        cache_ttl = self.cache_ttl['Min1']  # Sử dụng TTL cho M1

        # Kiểm tra cache
        if sym in self.price_cache:
            price, timestamp = self.price_cache[sym]
            # Nếu còn trong thời gian cache và là symbol đang có lệnh mở, ưu tiên dùng cache
            if now - timestamp < cache_ttl and sym in open_orders:
                return price
            # Nếu hết hạn cache hoặc không phải symbol đang có lệnh mở, xóa khỏi cache
            elif now - timestamp >= cache_ttl or sym not in open_orders:
                del self.price_cache[sym]

        # Lấy dữ liệu mới nếu không có trong cache hoặc cache đã hết hạn
        df_m1 = self.get_kline_data(sym, "Min1", limit=5)  # Giảm số lượng nến cần lấy
        if df_m1 is None or df_m1.empty:
            error_logger.warning(f"Không lấy được dữ liệu M1 cho {sym}")
            return None

        current_price = df_m1['close'].iloc[-1]

        # Quản lý kích thước cache
        if len(self.price_cache) >= self.max_cache_size:
            self.cleanup_cache()

        # Lưu vào cache
        self.price_cache[sym] = (current_price, now)
        return current_price

    def cleanup_cache(self) -> None:
        """Dọn dẹp cache theo chiến lược cải tiến"""
        now = time.time()
        symbols_to_remove = []

        # Xác định các symbol cần xóa khỏi cache
        for sym, (_, timestamp) in self.price_cache.items():
            # Ưu tiên giữ lại cache cho các symbol đang có lệnh mở
            if sym not in open_orders:
                symbols_to_remove.append(sym)
            # Xóa các cache đã hết hạn
            elif now - timestamp >= self.cache_ttl['default']:
                symbols_to_remove.append(sym)

        # Xóa các symbol đã xác định
        for sym in symbols_to_remove:
            del self.price_cache[sym]

        # Nếu cache vẫn quá lớn, xóa các mục cũ nhất
        if len(self.price_cache) >= self.max_cache_size:
            # Sắp xếp theo thời gian, cũ nhất lên đầu
            sorted_cache = sorted(self.price_cache.items(), key=lambda x: x[1][1])
            # Tính số lượng cần xóa để giảm kích thước cache xuống 80%
            excess_count = int(len(self.price_cache) * 0.2)
            # Đảm bảo xóa ít nhất 1 mục
            excess_count = max(1, excess_count)

            # Xóa các mục cũ nhất
            for sym, _ in sorted_cache[:excess_count]:
                del self.price_cache[sym]
                logic_logger.info(f"Đã xóa {sym} khỏi cache do vượt giới hạn kích thước")

    def calculate_total_equity(self) -> float:
        unrealized_pnl_total = 0.0
        for sym, order in open_orders.items():
            current_price = self.get_current_price(sym)
            if current_price is None:
                continue
            entry = order["entry"]
            volume = order["volume"]
            factor = SYMBOL_FACTOR_MAP.get(sym, 1.0)
            side = order["side"]
            if side == "LONG":
                pnl = (current_price - entry) * volume * factor
            else:
                pnl = (entry - current_price) * volume * factor
            unrealized_pnl_total += pnl
        return current_balance + unrealized_pnl_total

    def get_kline_data(self, sym: str, interval: str, limit: Optional[int] = None, start: Optional[int] = None, end: Optional[int] = None) -> Optional[pd.DataFrame]:
        """Lấy dữ liệu kline với cơ chế cache cải tiến"""
        now_utc = datetime.now(timezone.utc)

        # Kiểm tra symbol bị loại trừ
        if sym in excluded_symbols and now_utc < excluded_symbols[sym]:
            return None

        # Kiểm tra cache
        cache_key = (sym, interval)
        if cache_key in self.kline_cache:
            df_cached, cache_time = self.kline_cache[cache_key]
            # Sử dụng TTL tương ứng với khung thời gian
            cache_ttl_seconds = self.cache_ttl.get(interval, self.cache_ttl['default'])
            if (now_utc - cache_time).total_seconds() < cache_ttl_seconds:
                # Trả về bản sao để tránh thay đổi dữ liệu cache
                return df_cached.copy()

        try:
            # Chuẩn bị tham số API
            params = {'interval': interval}
            if limit:
                now_ts = int(now_utc.timestamp())
                interval_seconds = {'Min1': 60, 'Min5': 300, 'Min15': 900, 'Min30': 1800, 'Min60': 3600,
                                    'Hour4': 14400, 'Hour8': 28800, 'Day1': 86400}.get(interval, 60)
                # Thêm buffer để đảm bảo đủ dữ liệu
                params['start'] = now_ts - (limit + 5) * interval_seconds
                params['end'] = now_ts
            if start:
                params['start'] = start
            if end:
                params['end'] = end

            # Gọi API
            response = get_kline(sym, **params)
            if not response.get("success"):
                error_logger.error(f"Lỗi API MEXC cho {sym}: {response.get('msg', 'No message')}")
                excluded_symbols[sym] = now_utc + timedelta(minutes=EXCLUSION_DURATION_MIN)
                return None

            # Xử lý dữ liệu
            df = pd.DataFrame(response["data"])
            if df.empty:
                error_logger.warning(f"API trả về DataFrame rỗng cho {sym} ({interval})")
                return None

            # Chuẩn hóa dữ liệu
            df.rename(columns={'time': 'timestamp', 'vol': 'volume', 'amount': 'turnover'}, inplace=True)
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
            df.set_index('timestamp', inplace=True)
            df[['open', 'high', 'low', 'close', 'volume', 'turnover']] = df[['open', 'high', 'low', 'close', 'volume', 'turnover']].apply(pd.to_numeric, errors='coerce')
            df.dropna(subset=['open', 'high', 'low', 'close'], inplace=True)

            if df.empty:
                error_logger.warning(f"DataFrame rỗng sau khi xử lý cho {sym} ({interval})")
                return None

            # Lưu vào cache
            self.kline_cache[cache_key] = (df, now_utc)

            # Quản lý kích thước cache nếu cần
            if len(self.kline_cache) > self.max_cache_size:
                self._cleanup_kline_cache()

            return df
        except Exception as e:
            error_logger.error(f"Lỗi lấy kline cho {sym}: {e}")
            excluded_symbols[sym] = now_utc + timedelta(minutes=EXCLUSION_DURATION_MIN)
            return None

    def _cleanup_kline_cache(self) -> None:
        """Dọn dẹp cache kline khi kích thước vượt quá giới hạn"""
        # Lấy danh sách các mục trong cache
        cache_items = list(self.kline_cache.items())

        # Sắp xếp theo thời gian, cũ nhất lên đầu
        cache_items.sort(key=lambda x: x[1][1])

        # Xóa 20% cache cũ nhất
        items_to_remove = int(len(cache_items) * 0.2)
        items_to_remove = max(1, items_to_remove)  # Ít nhất xóa 1 item

        # Ghi log số lượng mục sẽ xóa
        logic_logger.debug(f"Dọn dẹp kline cache: xóa {items_to_remove}/{len(cache_items)} mục")

        # Xóa các mục cũ nhất
        for (sym, interval), _ in cache_items[:items_to_remove]:
            del self.kline_cache[(sym, interval)]
            logic_logger.debug(f"Đã xóa {sym} ({interval}) khỏi kline cache do vượt giới hạn kích thước")

    def get_price_precision(self, price: float) -> int:
        from decimal import Decimal
        try:
            price_decimal = Decimal(str(price))
            exponent = price_decimal.as_tuple().exponent
            return min(abs(exponent), 8) if isinstance(exponent, int) and exponent < 0 else 0
        except Exception:
            return 2

    def check_m1_conditions(self, sym: str) -> Tuple[bool, Optional[float], Optional[float], Optional[str]]:
        df_m1 = self.get_kline_data(sym, "Min1", limit=max(50, M1_VOLUME_AVG_PERIOD + 10))
        if df_m1 is None or df_m1.empty:
            return False, None, None, None

        rsi_14 = strategy.calculate_ha_rsi(df_m1.copy(), rsi_period=14, smooth_period=1)
        if rsi_14 is None:
            error_logger.warning(f"{sym} M1: Tính toán HA-RSI thất bại")
            return False, None, None, None

        if 'volume' not in df_m1.columns or len(df_m1['volume']) < M1_VOLUME_AVG_PERIOD + 1:
            error_logger.warning(f"{sym} M1: Dữ liệu volume không đủ (cần > {M1_VOLUME_AVG_PERIOD})")
            return False, rsi_14, None, None

        current_volume = df_m1['volume'].iloc[-1]
        avg_volume = df_m1['volume'].iloc[-(M1_VOLUME_AVG_PERIOD+1):-1].mean()

        if pd.isna(avg_volume) or avg_volume == 0:
            error_logger.warning(f"{sym} M1: Không tính được trung bình volume")
            volume_condition_met = False
        else:
            volume_condition_met = current_volume > avg_volume

        side = None
        if rsi_14 < -RSI_THRESHOLD:
            side = "LONG"
        elif rsi_14 > RSI_THRESHOLD:
            side = "SHORT"

        condition_met = side is not None and volume_condition_met
        current_price = df_m1['close'].iloc[-1] if not df_m1.empty else None
        return condition_met, rsi_14, current_price, side

    def update_watch_list(self, sym: str) -> None:
        if sym in open_orders or sym in excluded_symbols:
            return

        now_utc = datetime.now(timezone.utc)
        try:
            m1_condition_met, rsi_14, current_price, side = self.check_m1_conditions(sym)
            if m1_condition_met and current_price is not None and side is not None:
                if sym not in self.watch_list:
                    precision = self.get_price_precision(current_price)
                    self.watch_list[sym] = {
                        'side': side,
                        'added_time': now_utc,
                        'm1_rsi_at_add': rsi_14,
                        'price_at_add': current_price,
                        'precision': precision,
                        'last_check_time': now_utc
                    }
                    logic_logger.info(f"Thêm {sym} vào watchlist ({side}) - RSI: {rsi_14:.2f}")
                else:
                    self.watch_list[sym]['last_check_time'] = now_utc
        except Exception as e:
            error_logger.error(f"Lỗi cập nhật watchlist cho {sym}: {e}")
            excluded_symbols[sym] = now_utc + timedelta(minutes=EXCLUSION_DURATION_MIN)

    def cleanup_watch_list(self) -> None:
        now_utc = datetime.now(timezone.utc)
        symbols_to_remove = []
        for sym, data in self.watch_list.items():
            time_in_watchlist = now_utc - data['added_time']
            if time_in_watchlist.total_seconds() > WATCHLIST_RETENTION_MINUTES * 60:
                symbols_to_remove.append(sym)
                logic_logger.info(f"Xóa {sym} khỏi watchlist do hết thời gian")

        for sym in symbols_to_remove:
            if sym in self.watch_list:
                del self.watch_list[sym]

    def check_entry_conditions(self, sym: str, side: str) -> Tuple[bool, Optional[str], Optional[float]]:
        logic_logger.debug(f"Kiểm tra điều kiện vào lệnh M5 cho {sym} ({side})")
        m5_met, m5_reason, m5_entry_price = self._check_timeframe_conditions(sym, side, "Min5")
        if m5_met:
            return True, f"M5: {m5_reason}", m5_entry_price

        logic_logger.debug(f"Kiểm tra điều kiện vào lệnh M15 cho {sym} ({side})")
        m15_met, m15_reason, m15_entry_price = self._check_timeframe_conditions(sym, side, "Min15")
        if m15_met:
            return True, f"M15: {m15_reason}", m15_entry_price

        return False, "Điều kiện M5/M15 không đạt", None

    def _check_timeframe_conditions(self, sym: str, side: str, interval: str) -> Tuple[bool, Optional[str], Optional[float]]:
        df = self.get_kline_data(sym, interval, limit=max(50, ATR_PERIOD + ENTRY_ATR_AVG_PERIOD + 2))
        min_required_candles = max(3, ATR_PERIOD + 1, ENTRY_ATR_AVG_PERIOD + 1, ENTRY_VOL_AVG_PERIOD + 1)
        if df is None or len(df) < min_required_candles:
            return False, f"{interval} dữ liệu không đủ (cần {min_required_candles})", None

        interval_seconds = {'Min5': 300, 'Min15': 900}.get(interval, 60)
        last_candle_time = df.index[-1].to_pydatetime()
        if (datetime.now(timezone.utc) - last_candle_time).total_seconds() > 2.5 * interval_seconds:
            return False, f"{interval} dữ liệu cũ", None

        current_price = df['close'].iloc[-1]
        precision = self.get_price_precision(current_price)

        candle_pattern_ok = False
        try:
            low_n1, low_n2, low_n3 = df['low'].iloc[-1], df['low'].iloc[-2], df['low'].iloc[-3]
            high_n1, high_n2, high_n3 = df['high'].iloc[-1], df['high'].iloc[-2], df['high'].iloc[-3]
            tolerance = current_price * (ENTRY_CANDLE_TOLERANCE_PCT / 100.0)
            if side == "LONG":
                c1 = low_n1 >= low_n2 - tolerance
                c2 = low_n2 >= low_n3 - tolerance
                if c1 or c2:
                    candle_pattern_ok = True
            elif side == "SHORT":
                c1 = high_n1 <= high_n2 + tolerance
                c2 = high_n2 <= high_n3 + tolerance
                if c1 or c2:
                    candle_pattern_ok = True
        except IndexError:
            candle_pattern_ok = False
        if not candle_pattern_ok:
            return False, f"{interval} mô hình nến không đạt", None

        atr = strategy.calculate_atr(df.copy(), period=ATR_PERIOD)
        if atr is None:
            return False, f"{interval} tính toán ATR thất bại", None

        # Kiểm tra biến động giảm
        try:
            # Kiểm tra ATR giảm so với trung bình
            if len(df) >= ATR_PERIOD + ENTRY_ATR_AVG_PERIOD + 1:
                atr_series = ATR(df['high'], df['low'], df['close'], timeperiod=ATR_PERIOD)
                avg_atr_prev_N = atr_series.iloc[-(ENTRY_ATR_AVG_PERIOD+1):-1].mean()
                if pd.isna(avg_atr_prev_N) or atr >= avg_atr_prev_N:
                    return False, f"{interval} biến động chưa giảm (ATR hiện tại >= trung bình)", None
            # Hoặc kiểm tra ATR giảm so với nến trước
            elif len(df) >= ATR_PERIOD + 2:
                atr_series = ATR(df['high'], df['low'], df['close'], timeperiod=ATR_PERIOD)
                atr_prev = atr_series.iloc[-2]
                if pd.isna(atr_prev) or atr >= atr_prev:
                    return False, f"{interval} biến động chưa giảm (ATR hiện tại >= ATR trước)", None
        except Exception:
            return False, f"{interval} biến động không giảm (ATR)", None

        # Kiểm tra volume giảm
        try:
            if 'volume' not in df.columns:
                return False, f"{interval} thiếu dữ liệu volume", None

            # Kiểm tra volume giảm so với trung bình
            if len(df['volume']) >= ENTRY_VOL_AVG_PERIOD + 1:
                vol_n1 = df['volume'].iloc[-1]
                avg_vol_prev_N = df['volume'].iloc[-(ENTRY_VOL_AVG_PERIOD+1):-1].mean()
                if pd.isna(avg_vol_prev_N) or vol_n1 >= avg_vol_prev_N:
                    return False, f"{interval} volume chưa giảm (hiện tại >= trung bình)", None
            # Hoặc kiểm tra volume giảm so với nến trước
            elif len(df['volume']) >= 2:
                vol_n1, vol_n2 = df['volume'].iloc[-1], df['volume'].iloc[-2]
                if vol_n1 >= vol_n2:
                    return False, f"{interval} volume chưa giảm (hiện tại >= trước)", None
        except Exception:
            return False, f"{interval} xác nhận volume thất bại", None

        entry_offset_factor = 0.0002
        entry_price = round(
            current_price * (1 - entry_offset_factor) if side == "LONG" else current_price * (1 + entry_offset_factor),
            precision
        )
        return True, f"{interval} điều kiện đạt", entry_price

    def place_trade_from_watchlist(self, sym: str) -> None:
        if sym not in self.watch_list:
            return
        if sym in open_orders:
            if sym in self.watch_list:
                del self.watch_list[sym]
            return

        data = self.watch_list[sym]
        side = data['side']
        added_time = data['added_time']
        price_at_add = data['price_at_add']
        now_utc = datetime.now(timezone.utc)
        time_in_watchlist = (now_utc - added_time).total_seconds() / 60

        if time_in_watchlist < MIN_WATCHLIST_DURATION_MIN:
            return

        df_m1 = self.get_kline_data(sym, "Min1", limit=10)
        if df_m1 is None or df_m1.empty:
            return
        current_price = df_m1['close'].iloc[-1]
        price_change_pct = abs((current_price - price_at_add) / price_at_add * 100)
        if price_change_pct > MAX_PRICE_DEVIATION_PERCENT:
            del self.watch_list[sym]
            return

        try:
            # Kiểm tra điều kiện vào lệnh
            entry_conditions_met, entry_reason, entry_price = self.check_entry_conditions(sym, side)

            # Nếu điều kiện đạt và có giá vào lệnh
            if entry_conditions_met and entry_price is not None:
                # Lấy độ chính xác giá
                precision = data.get('precision', self.get_price_precision(entry_price))

                # Lấy hệ số khối lượng
                factor = SYMBOL_FACTOR_MAP.get(sym)
                if factor is None:
                    error_logger.error(f"Không tìm thấy volume factor cho {sym}")
                    return

                # Tính khối lượng lệnh
                volume = int((BASE_USDT * BASE_LEVERAGE) / (entry_price * factor))
                if volume <= 0:
                    error_logger.warning(f"{sym}: Volume tính được không hợp lệ ({volume})")
                    return

                # Tính SL và TP
                sl, tp = self.calculate_sl_tp(sym, side, entry_price, precision)
                if sl is None or tp is None:
                    logic_logger.warning(f"{sym}: Không tính được SL/TP hợp lệ")
                    return

                # Tính PNL dự kiến
                tp_pnl = abs(tp - entry_price) * volume * factor
                sl_pnl = -abs(sl - entry_price) * volume * factor

                # Ghi log lý do vào lệnh
                logic_logger.info(f"Vào lệnh {sym} ({side}) với lý do: {entry_reason}")

                # Thực hiện đặt lệnh
                self.execute_order(sym, side, entry_price, tp, sl, volume, tp_pnl, sl_pnl)

                # Xóa khỏi watchlist sau khi đặt lệnh
                if sym in self.watch_list:
                    del self.watch_list[sym]
        except Exception as e:
            error_logger.error(f"Lỗi trong place_trade_from_watchlist cho {sym}: {e}")
            excluded_symbols[sym] = now_utc + timedelta(minutes=EXCLUSION_DURATION_MIN)

    def calculate_sl_tp(self, sym: str, side: str, entry_price: float, precision: int) -> Tuple[Optional[float], Optional[float]]:
        df_sl = self.get_kline_data(sym, ATR_TIMEFRAME, limit=max(50, ATR_PERIOD + 5))
        if df_sl is None or df_sl.empty:
            return None, None

        atr = strategy.calculate_atr(df_sl.copy(), period=ATR_PERIOD)
        if atr is None or atr <= 0:
            return None, None

        sl_distance_atr = atr * ATR_SL_MULTIPLIER
        sl_distance_min_pct = entry_price * MIN_SL_PERCENTAGE
        sl_distance = max(sl_distance_atr, sl_distance_min_pct)

        if side == "LONG":
            sl_price = entry_price - sl_distance
            tp_price = entry_price + sl_distance * RR_RATIO
        else:
            sl_price = entry_price + sl_distance
            tp_price = entry_price - sl_distance * RR_RATIO

        sl_price_rounded = round(sl_price, precision)
        tp_price_rounded = round(tp_price, precision)

        buffer = 1 / (10**(precision + 1))
        if side == "LONG":
            if sl_price_rounded >= entry_price - buffer:
                sl_price_rounded = round(entry_price - sl_distance_min_pct, precision)
            if tp_price_rounded <= entry_price + buffer:
                return None, None
        elif side == "SHORT":
            if sl_price_rounded <= entry_price + buffer:
                sl_price_rounded = round(entry_price + sl_distance_min_pct, precision)
            if tp_price_rounded >= entry_price - buffer:
                return None, None

        return sl_price_rounded, tp_price_rounded

    def execute_order(self, sym: str, side: str, entry: float, tp: float, sl: float, volume: int, tp_pnl: float, sl_pnl: float) -> None:
        global current_balance
        precision = self.get_price_precision(entry)
        now_utc = datetime.now(timezone.utc)

        # Chuẩn bị tham số cho place_order
        is_long = side == "LONG"
        is_market = False  # Giả sử dùng limit order
        symbol_base = sym  # Ví dụ: BTC_USDT

        # Gọi place_order từ basic_call.py
        import basic_call
        old_post_method = basic_call.post_method
        def wrapped_post_method(obj):
            return old_post_method(obj, discord_bot=self.discord)
        basic_call.post_method = wrapped_post_method
        success, result = place_order(
            symbol=symbol_base,
            price=entry,
            tp=tp,
            sl=sl,
            volume=volume,
            leverage=BASE_LEVERAGE,
            is_market=is_market,
            is_long=is_long
        )
        basic_call.post_method = old_post_method

        # Lấy order ID từ kết quả
        order_id = result.get("data", {}).get("orderId") if success else None

        # Chuẩn bị tin nhắn Telegram
        total_equity = self.calculate_total_equity()
        symbol_display = sym.split('_')[0]
        order_msg_base = (
            f"**{'🟢' if side == 'LONG' else '🔴'} #NEW_ORDER**\n"
            f"💱 Symbol: #{symbol_display}\n"
            f"📊 Side: **{side}**\n"
            f"🎯 Entry: {entry:.{precision}f}\n"
            f"✅ TP: {tp:.{precision}f} (*~+{tp_pnl:.2f} USDT*)\n"
            f"❌ SL: {sl:.{precision}f} (*~{sl_pnl:.2f} USDT*)\n"
            f"💲 Value: {BASE_USDT:.2f} USDT (x{BASE_LEVERAGE} Lev)\n"
            f"⏰ Time: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
            f"🏦 Balance: {current_balance:.2f} USDT\n"
            f"💰 Unrealized PNL: {(total_equity - current_balance):.2f} USDT\n"
            f"📈 Total Equity: {total_equity:.2f} USDT"
        )

        if success and order_id:
            # Lưu thông tin lệnh
            open_orders[sym] = {
                "symbol": sym,
                "side": side,
                "entry": entry,
                "tp": tp,
                "sl": sl,
                "volume": volume,
                "order_id": order_id,
                "open_time": now_utc,
                "precision": precision,
                "tp_pnl_est": tp_pnl,
                "sl_pnl_est": sl_pnl
            }
            self.order_warnings_sent[order_id] = {ORDER_WARN_DURATION_H1: False, ORDER_WARN_DURATION_H2: False}
            self.send_discord_message(order_msg_base + f"\n**✅ Order Placed Successfully! Order ID: {order_id}**")
            trade_logger.info(f"Đặt lệnh thành công {order_id} cho {sym} ({side})")
        else:
            error_msg = result.get("msg", "Lỗi không xác định") if not success else "Không tìm thấy Order ID"
            self.send_discord_message(order_msg_base + f"\n**❌ Order Placement Failed! Error: {error_msg}**")
            trade_logger.error(f"Không thể đặt lệnh cho {sym} ({side}): {error_msg}")
            excluded_symbols[sym] = now_utc + timedelta(minutes=EXCLUSION_DURATION_MIN)

    def manage_open_orders(self) -> None:
        """Quản lý các lệnh đang mở - phương thức chính"""
        # Nếu không có lệnh mở, không cần xử lý
        if not open_orders:
            return

        now_utc = datetime.now(timezone.utc)

        # Duyệt qua danh sách lệnh mở
        for sym in list(open_orders.keys()):
            # Kiểm tra lại vì lệnh có thể đã bị đóng trong quá trình xử lý
            if sym not in open_orders:
                continue

            order = open_orders[sym]
            order_id = order["order_id"]
            open_time = order["open_time"]
            duration = now_utc - open_time

            try:
                # Lấy dữ liệu thị trường hiện tại
                market_data = self._get_current_market_data(sym, order_id)
                if market_data is None:
                    continue

                current_price, precision, recent_high, recent_low = market_data

                # Kiểm tra điều kiện đóng lệnh
                closed = self.check_order_closure(sym, order, current_price, precision, recent_high, recent_low)
                if closed:
                    if order_id in self.order_warnings_sent:
                        del self.order_warnings_sent[order_id]
                    continue

                # Kiểm tra cảnh báo thời gian mở lệnh
                self.check_order_duration_warnings(order_id, sym, duration)

            except Exception as e:
                error_logger.error(f"Lỗi khi quản lý lệnh mở {sym} ({order_id}): {e}", exc_info=True)
                excluded_symbols[sym] = now_utc + timedelta(minutes=EXCLUSION_DURATION_MIN)

    def _get_current_market_data(self, sym: str, order_id: str) -> Optional[Tuple[float, int, float, float]]:
        """Lấy dữ liệu thị trường hiện tại cho một symbol"""
        now_utc = datetime.now(timezone.utc)

        # Lấy dữ liệu kline M1
        df_m1 = self.get_kline_data(sym, "Min1", limit=5)
        if df_m1 is None or df_m1.empty:
            error_logger.warning(f"Không lấy được dữ liệu M1 để quản lý lệnh {sym} ({order_id})")
            return None

        # Kiểm tra dữ liệu có mới không
        last_candle_time_m1 = df_m1.index[-1].to_pydatetime()
        if (now_utc - last_candle_time_m1).total_seconds() > 150:
            error_logger.warning(f"{sym} dữ liệu M1 có vẻ cũ (nến cuối: {last_candle_time_m1})")
            return None

        # Tính toán các giá trị cần thiết
        recent_high = df_m1['high'].iloc[-3:].max()
        recent_low = df_m1['low'].iloc[-3:].min()
        current_price = df_m1['close'].iloc[-1]

        # Lấy độ chính xác giá
        if sym in open_orders:
            precision = open_orders[sym].get('precision', self.get_price_precision(current_price))
        else:
            precision = self.get_price_precision(current_price)

        return current_price, precision, recent_high, recent_low

    def check_order_closure(self, sym: str, order: dict, current_price: float, precision: int, recent_high: float, recent_low: float) -> bool:
        global current_balance
        if sym not in open_orders or order['order_id'] != open_orders[sym]['order_id']:
            error_logger.warning(f"Lệnh {order['order_id']} cho {sym} có vẻ đã đóng hoặc thay đổi")
            return False

        entry, tp, sl = order["entry"], order["tp"], order["sl"]
        volume, side = order["volume"], order["side"]
        order_id = order["order_id"]
        open_time = order["open_time"]
        factor = SYMBOL_FACTOR_MAP.get(sym, 1.0)
        now_utc = datetime.now(timezone.utc)

        hit_tp, hit_sl = False, False
        close_price = current_price
        if side == "LONG":
            if recent_high >= tp:
                hit_tp = True
                close_price = tp
            elif recent_low <= sl:
                hit_sl = True
                close_price = sl
        elif side == "SHORT":
            if recent_low <= tp:
                hit_tp = True
                close_price = tp
            elif recent_high >= sl:
                hit_sl = True
                close_price = sl

        if not hit_tp and not hit_sl:
            return False

        pnl = (close_price - entry) * volume * factor if side == "LONG" else (entry - close_price) * volume * factor
        status = f"✅ TP Đạt tại {tp:.{precision}f}" if hit_tp else f"❌ SL Đạt tại {sl:.{precision}f}"
        previous_balance = current_balance
        current_balance += pnl

        symbol_base = sym.split('_')[0]
        duration = now_utc - open_time
        total_seconds = int(duration.total_seconds())
        hours, rem = divmod(total_seconds, 3600)
        minutes, seconds = divmod(rem, 60)
        duration_str = f"{hours}h {minutes}m {seconds}s"
        total_equity = self.calculate_total_equity()

        close_msg = (
            f"**{'✅' if hit_tp else '❌'} #CLOSED_ORDER**\n"
            f"💱 Symbol: #{symbol_base}\n"
            f"📊 Side: {side}\n"
            f"🎯 Entry: {entry:.{precision}f}\n"
            f"🔄 Status: {status} (Trigger Price: {close_price:.{precision}f})\n"
            f"💰 PNL: {pnl:+.2f} USDT\n"
            f"⏱️ Duration: {duration_str}\n"
            f"💲 Value: {BASE_USDT:.2f} USDT\n"
            f"🏦 Balance: {previous_balance:.2f} -> **{current_balance:.2f} USDT**\n"
            f"💰 Unrealized PNL: {(total_equity - current_balance):.2f} USDT\n"
            f"📈 Total Equity: {total_equity:.2f} USDT"
        )
        self.send_discord_message(close_msg)
        trade_logger.info(f"Đã đóng lệnh {order_id} cho {sym} ({side}) với trạng thái {status}, PNL: {pnl:.2f} USDT")

        if sym in open_orders and open_orders[sym]['order_id'] == order_id:
            del open_orders[sym]
            self.cleanup_cache()

        if current_balance < 0:
            self.send_discord_message(f"**⚠️ CẢNH BÁO NGHIÊM TRỌNG**\nSố dư tài khoản âm: {current_balance:.2f} USDT")
        return True

    def check_order_duration_warnings(self, order_id: str, sym: str, duration: timedelta) -> None:
        if sym not in open_orders or open_orders[sym]['order_id'] != order_id:
            if order_id in self.order_warnings_sent:
                del self.order_warnings_sent[order_id]
            return

        order = open_orders[sym]
        precision = order.get('precision', 2)
        duration_hours = duration.total_seconds() / 3600

        if order_id not in self.order_warnings_sent:
            self.order_warnings_sent[order_id] = {ORDER_WARN_DURATION_H1: False, ORDER_WARN_DURATION_H2: False}

        symbol_base = sym.split('_')[0]

        h1_warn_key = ORDER_WARN_DURATION_H1
        if duration_hours >= h1_warn_key and not self.order_warnings_sent[order_id].get(h1_warn_key, False):
            warn_msg = (
                f"**⚠️ #ORDER_DURATION_WARNING**\n"
                f"💱 Symbol: #{symbol_base}\n"
                f"⏱️ Thời gian mở: > {h1_warn_key} giờ ({duration_hours:.1f}h)\n"
                f"📊 Side: {order['side']} | Entry: {order['entry']:.{precision}f}\n"
                f"✅ TP: {order['tp']:.{precision}f} | ❌ SL: {order['sl']:.{precision}f}"
            )
            self.send_discord_message(warn_msg)
            trade_logger.warning(f"Đã gửi cảnh báo thời gian {h1_warn_key}h cho lệnh {order_id} ({sym})")
            self.order_warnings_sent[order_id][h1_warn_key] = True

        h2_warn_key = ORDER_WARN_DURATION_H2
        if duration_hours >= h2_warn_key and not self.order_warnings_sent[order_id].get(h2_warn_key, False):
            warn_msg = (
                f"**🚨 #ORDER_DURATION_ALERT**\n"
                f"💱 Symbol: #{symbol_base}\n"
                f"⏱️ Thời gian mở: > {h2_warn_key} giờ ({duration_hours:.1f}h)\n"
                f"📊 Side: {order['side']} | Entry: {order['entry']:.{precision}f}\n"
                f"✅ TP: {order['tp']:.{precision}f} | ❌ SL: {order['sl']:.{precision}f}"
            )
            self.send_discord_message(warn_msg)
            trade_logger.warning(f"Đã gửi cảnh báo thời gian {h2_warn_key}h cho lệnh {order_id} ({sym})")
            self.order_warnings_sent[order_id][h2_warn_key] = True

    def send_open_orders_summary(self) -> None:
        global last_summary_time
        now_utc = datetime.now(timezone.utc)
        if (now_utc - last_summary_time).total_seconds() < ORDER_SUMMARY_INTERVAL_MIN * 60:
            return

        # Chỉ gửi summary nếu có lệnh đang mở
        if not open_orders:
            last_summary_time = now_utc
            return

        unrealized_pnl_total = 0.0
        open_orders_summary = []

        for sym, order in open_orders.items():
            current_price = self.get_current_price(sym)
            if current_price is None:
                error_logger.warning(f"Không lấy được giá hiện tại cho {sym} trong tóm tắt")
                continue
            entry = order["entry"]
            volume = order["volume"]
            factor = SYMBOL_FACTOR_MAP.get(sym, 1.0)
            side = order["side"]
            precision = order.get('precision', 2)
            duration = now_utc - order['open_time']
            total_seconds = int(duration.total_seconds())
            hours, rem = divmod(total_seconds, 3600)
            minutes, seconds = divmod(rem, 60)
            duration_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

            if side == "LONG":
                pnl = (current_price - entry) * volume * factor
            else:
                pnl = (entry - current_price) * volume * factor
            unrealized_pnl_total += pnl

            symbol_base = sym.split('_')[0]
            open_orders_summary.append(
                f"🔹 #{symbol_base} ({side})\n"
                f"   Entry: {entry:.{precision}f} | Current: {current_price:.{precision}f}\n"
                f"   PNL: {pnl:.2f} USDT | Dur: {duration_str}\n"
                f"   TP: {order['tp']:.{precision}f} | SL: {order['sl']:.{precision}f}\n"
            )

        total_equity = current_balance + unrealized_pnl_total
        summary_msg = (
            f"**📊 #ORDER_SUMMARY**\n"
            f"⏰ Time: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
            f"🏦 Balance: {current_balance:.2f} USDT\n"
            f"💰 Unrealized PNL: {unrealized_pnl_total:.2f} USDT\n"
            f"📈 Total Equity: {total_equity:.2f} USDT\n"
            f"📈 Open Orders: {len(open_orders)}\n\n"
        )

        summary_msg += "\n".join(open_orders_summary)

        self.send_discord_message(summary_msg)
        last_summary_time = now_utc

    def handle_dashboard_command(self, _user_id: str, _args: str, _message) -> str:
        """Xử lý lệnh dashboard từ Discord"""
        try:
            # Dừng dashboard hiện tại nếu có
            if self.dashboard_thread and self.dashboard_thread.is_alive():
                self.stop_dashboard()
                time.sleep(1)

            # Khởi động dashboard mới
            self.start_dashboard()
            return "✅ Dashboard đã được khởi động và sẽ tự động cập nhật mỗi 60 giây."
        except Exception as e:
            error_logger.error(f"Lỗi khởi động dashboard: {e}")
            return f"❌ Lỗi khởi động dashboard: {str(e)}"

    def cleanup(self):
        """Dọn dẹp tài nguyên khi dừng bot"""
        # Dừng dashboard trước
        self.stop_dashboard()

        if hasattr(self, 'discord'):
            self.discord.stop_polling_updates()

    def run(self) -> None:
        """Phương thức chính để chạy bot"""
        trade_logger.info("Khởi động Trading Bot...")
        self._send_startup_message()

        try:
            while True:
                try:
                    # Thực hiện một chu kỳ xử lý
                    self._process_trading_cycle()

                    # Nghỉ giữa các chu kỳ
                    time.sleep(SLEEP_INTERVAL)

                except KeyboardInterrupt:
                    trade_logger.info("Nhận tín hiệu dừng từ bàn phím. Tắt bot...")
                    self.send_discord_message("**🤖 Trading Bot Đang Tắt**")
                    break
                except Exception as e:
                    error_logger.exception(f"Lỗi trong vòng lặp chính: {e}")
                    self.send_discord_message(f"**🆘 LỖI BOT:**\n```{str(e)[:300]}```")
                    time.sleep(SLEEP_INTERVAL * 2)
        finally:
            self.cleanup()

    def _send_startup_message(self) -> None:
        """Gửi thông báo khởi động bot"""
        # Đợi Discord bot sẵn sàng
        if not self.discord.wait_for_ready(timeout=30):
            error_logger.error("Discord bot không sẵn sàng sau 30 giây")
            return

        self.send_discord_message(
            f"**🤖 Trading Bot Khởi tạo**\n"
            f"⏰ Time: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
            f"⚙️ Config: RR={RR_RATIO}, SL ATR={ATR_SL_MULTIPLIER}x({ATR_TIMEFRAME})"
        )

        # Hiển thị bàn phím lệnh
        self.discord.send_main_keyboard()

    def _process_trading_cycle(self) -> None:
        """Xử lý một chu kỳ giao dịch hoàn chỉnh"""
        now_utc = datetime.now(timezone.utc)

        # Bước 1: Dọn dẹp danh sách loại trừ
        self._cleanup_excluded_symbols(now_utc)

        # Bước 2: Cập nhật watchlist
        self._update_watchlists()

        # Bước 3: Kiểm tra và đặt lệnh từ watchlist (chỉ khi bot không bị tạm dừng)
        if not self.is_paused:
            self._process_watchlist_entries()
        else:
            logic_logger.debug("Bot đang tạm dừng, bỏ qua việc đặt lệnh mới")

        # Bước 4: Quản lý các lệnh đang mở (luôn thực hiện, ngay cả khi bot bị tạm dừng)
        if open_orders:
            self.manage_open_orders()

        # Bước 5: Dọn dẹp watchlist và gửi tóm tắt
        self.cleanup_watch_list()
        self.send_open_orders_summary()

    def _cleanup_excluded_symbols(self, now_utc: datetime) -> None:
        """Dọn dẹp danh sách các symbol bị loại trừ đã hết hạn"""
        expired_exclusions = [sym for sym, expiry in excluded_symbols.items() if now_utc > expiry]
        for sym in expired_exclusions:
            del excluded_symbols[sym]
            logic_logger.info(f"Xóa {sym} khỏi danh sách loại trừ")

    def _update_watchlists(self) -> None:
        """Cập nhật watchlist với các symbol mới"""
        # Lọc các symbol cần kiểm tra (không trong open_orders và excluded_symbols)
        symbols_to_check = [s for s in MEXC_SYMBOL if s not in open_orders and s not in excluded_symbols]

        if symbols_to_check:
            # Tính toán thời gian nghỉ giữa các lần kiểm tra để tránh quá tải API
            sleep_time = max(0.05, min(0.25, 5 / len(MEXC_SYMBOL)))

            # Kiểm tra từng symbol
            for sym in symbols_to_check:
                self.update_watch_list(sym)
                time.sleep(sleep_time)

    def _process_watchlist_entries(self) -> None:
        """Xử lý các entry từ watchlist"""
        if not self.watch_list:
            return

        # Tạo bản sao danh sách để tránh lỗi khi xóa trong quá trình lặp
        watchlist_keys = list(self.watch_list.keys())

        for sym in watchlist_keys:
            # Kiểm tra lại điều kiện trước khi xử lý
            if sym in self.watch_list and sym not in open_orders and sym not in excluded_symbols:
                self.place_trade_from_watchlist(sym)
                # Nghỉ ngẫu nhiên để tránh quá tải API
                time.sleep(random.uniform(0.2, 0.4))

if __name__ == "__main__":
    bot = TradingBot()
    try:
        bot.run()
    except KeyboardInterrupt:
        bot.cleanup()