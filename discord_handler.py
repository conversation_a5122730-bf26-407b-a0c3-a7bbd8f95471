import discord
from discord.ext import commands
import logging
import asyncio
import threading
from typing import Callable, Optional
import queue

logger = logging.getLogger('discord')

class DiscordHandler:
    def __init__(self, token: str, admin_id: str, guild_id: str, channel_name: str = "mexc-auto"):
        self.token = token
        self.admin_id = int(admin_id)
        self.guild_id = int(guild_id)
        self.channel_name = channel_name
        self.command_handlers = {}
        self.message_queue = queue.Queue()
        self.stop_bot = False
        self.bot_thread = None
        self.channel = None

        intents = discord.Intents.default()
        intents.message_content = False  # Không sử dụng privileged intent
        intents.members = False  # Không cần privileged intent
        intents.presences = False  # Không cần privileged intent
        self.bot = commands.Bot(command_prefix='/', intents=intents)

        self.register_command('start', self._handle_start)
        self.register_command('help', self._handle_help)
        self.register_command('keyboard', self._handle_keyboard)

        self._setup_bot_events()
        self._setup_slash_commands()
        logger.info(f"DiscordHandler khởi tạo cho Guild {guild_id}, Channel {channel_name}")

    def _setup_bot_events(self):
        @self.bot.event
        async def on_ready():
            logger.info(f'Discord bot đã kết nối: {self.bot.user}')
            guild = self.bot.get_guild(self.guild_id)
            if guild:
                self.channel = discord.utils.get(guild.channels, name=self.channel_name)
                if self.channel:
                    logger.info(f'Đã tìm thấy channel: {self.channel_name}')
                    # Sync slash commands với guild
                    try:
                        synced = await self.bot.tree.sync(guild=guild)
                        logger.info(f"Đã sync {len(synced)} slash commands với guild {guild.name}")
                    except Exception as e:
                        logger.error(f"Lỗi sync slash commands: {e}")
                else:
                    logger.error(f'Không tìm thấy channel: {self.channel_name}')
            else:
                logger.error(f'Không tìm thấy guild với ID: {self.guild_id}')

        @self.bot.event
        async def on_message(message):
            if message.author == self.bot.user:
                return

            if message.author.id != self.admin_id:
                logger.warning(f"Tin nhắn từ người dùng không được phép: {message.author.id}")
                return

            # Chỉ phản hồi khi được mention trong channel chính
            if self.bot.user in message.mentions and message.channel == self.channel:
                await self.send_message("👋 Xin chào! Sử dụng slash commands (/) để tương tác với bot.")

    def _setup_slash_commands(self):
        """Thiết lập slash commands cho Discord bot"""

        @self.bot.tree.command(name="start", description="Khởi động bot")
        async def start_command(interaction: discord.Interaction):
            if interaction.user.id != self.admin_id:
                await interaction.response.send_message("❌ Bạn không có quyền sử dụng lệnh này.", ephemeral=True)
                return

            result = self._handle_start(str(interaction.user.id), "", interaction)
            await interaction.response.send_message(result)

        @self.bot.tree.command(name="help", description="Hiển thị danh sách lệnh")
        async def help_command(interaction: discord.Interaction):
            if interaction.user.id != self.admin_id:
                await interaction.response.send_message("❌ Bạn không có quyền sử dụng lệnh này.", ephemeral=True)
                return

            result = self._handle_help(str(interaction.user.id), "", interaction)
            await interaction.response.send_message(result)

    async def _process_command(self, message):
        content = message.content[1:]
        command_parts = content.split(' ', 1)
        command = command_parts[0].lower()
        args = command_parts[1] if len(command_parts) > 1 else ""
        
        if command in self.command_handlers:
            try:
                result = self.command_handlers[command](str(message.author.id), args, message)
                if result:
                    await self.send_message(result)
            except Exception as e:
                logger.error(f"Lỗi khi xử lý lệnh {command}: {e}")
                await self.send_message(f"⚠️ Lỗi xử lý lệnh: {str(e)}")
        else:
            await self.send_message(f"❓ Lệnh không được hỗ trợ: {command}")

    async def send_message(self, message: str) -> bool:
        if not self.channel:
            logger.warning("Channel chưa được thiết lập")
            return False
        
        if len(message) > 2000:
            message = message[:1997] + "..."
        
        try:
            await self.channel.send(message)
            return True
        except Exception as e:
            logger.error(f"Lỗi gửi tin nhắn Discord: {e}")
            return False

    def send_message_sync(self, message: str) -> bool:
        if not self.bot.is_ready():
            logger.warning("Bot chưa sẵn sàng")
            return False

        if not hasattr(self.bot, 'loop') or not self.bot.loop:
            logger.warning("Bot loop chưa sẵn sàng")
            return False

        try:
            future = asyncio.run_coroutine_threadsafe(self.send_message(message), self.bot.loop)
            return future.result(timeout=10)
        except Exception as e:
            logger.error(f"Lỗi gửi tin nhắn sync: {e}")
            return False

    async def send_message_and_get_id(self, message: str) -> Optional[int]:
        """Gửi tin nhắn và trả về message ID"""
        if not self.channel:
            logger.warning("Channel chưa được thiết lập")
            return None

        if len(message) > 2000:
            message = message[:1997] + "..."

        try:
            sent_message = await self.channel.send(message)
            return sent_message.id
        except Exception as e:
            logger.error(f"Lỗi gửi tin nhắn Discord: {e}")
            return None

    def send_message_and_get_id_sync(self, message: str) -> Optional[int]:
        """Gửi tin nhắn sync và trả về message ID"""
        if not self.bot.is_ready():
            logger.warning("Bot chưa sẵn sàng")
            return None

        if not hasattr(self.bot, 'loop') or not self.bot.loop:
            logger.warning("Bot loop chưa sẵn sàng")
            return None

        try:
            future = asyncio.run_coroutine_threadsafe(self.send_message_and_get_id(message), self.bot.loop)
            return future.result(timeout=10)
        except Exception as e:
            logger.error(f"Lỗi gửi tin nhắn sync: {e}")
            return None

    async def edit_message(self, message_id: int, new_content: str) -> bool:
        """Edit một tin nhắn đã gửi"""
        if not self.channel:
            logger.warning("Channel chưa được thiết lập")
            return False

        if len(new_content) > 2000:
            new_content = new_content[:1997] + "..."

        try:
            message = await self.channel.fetch_message(message_id)
            await message.edit(content=new_content)
            return True
        except Exception as e:
            logger.error(f"Lỗi edit tin nhắn Discord: {e}")
            return False

    def edit_message_sync(self, message_id: int, new_content: str) -> bool:
        """Edit tin nhắn sync"""
        if not self.bot.is_ready():
            logger.warning("Bot chưa sẵn sàng")
            return False

        if not hasattr(self.bot, 'loop') or not self.bot.loop:
            logger.warning("Bot loop chưa sẵn sàng")
            return False

        try:
            future = asyncio.run_coroutine_threadsafe(self.edit_message(message_id, new_content), self.bot.loop)
            return future.result(timeout=10)
        except Exception as e:
            logger.error(f"Lỗi edit tin nhắn sync: {e}")
            return False

    def start_polling(self):
        if self.bot_thread and self.bot_thread.is_alive():
            logger.warning("Discord bot đã đang chạy")
            return
        
        self.stop_bot = False
        self.bot_thread = threading.Thread(target=self._run_bot, daemon=True)
        self.bot_thread.start()
        logger.info("Đã bắt đầu Discord bot")

    def _run_bot(self):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            logger.info("Bắt đầu kết nối Discord bot...")
            loop.run_until_complete(self.bot.start(self.token))
        except discord.LoginFailure as e:
            logger.error(f"Token Discord không hợp lệ: {e}")
        except discord.HTTPException as e:
            logger.error(f"Lỗi HTTP Discord: {e}")
        except Exception as e:
            logger.error(f"Lỗi chạy Discord bot: {e}")
        finally:
            if not loop.is_closed():
                loop.close()

    def stop_polling_updates(self):
        self.stop_bot = True
        if self.bot_thread and self.bot_thread.is_alive():
            try:
                if hasattr(self.bot, 'loop') and self.bot.loop and not self.bot.loop.is_closed():
                    asyncio.run_coroutine_threadsafe(self.bot.close(), self.bot.loop)
                self.bot_thread.join(timeout=5)
            except Exception as e:
                logger.error(f"Lỗi khi dừng Discord bot: {e}")
            logger.info("Đã dừng Discord bot")

    def register_command(self, command: str, handler: Callable):
        self.command_handlers[command] = handler
        logger.debug(f"Đã đăng ký lệnh: {command}")

    def _handle_start(self, _user_id: str, _args: str, _message) -> str:
        return "🤖 **Trading Bot Discord**\n\nBot đã sẵn sàng hoạt động!\nSử dụng /help để xem danh sách lệnh."

    def _handle_help(self, _user_id: str, _args: str, _message) -> str:
        help_text = """
🤖 **DANH SÁCH LỆNH BOT**

**📊 Thông tin:**
/status - Trạng thái bot
/balance - Số dư tài khoản
/stats - Thống kê giao dịch
/dashboard - Khởi động dashboard tự động cập nhật
/orders - Lệnh đang mở
/watchlist - Danh sách theo dõi
/openpositions - Vị thế đang mở

**⚙️ Cấu hình:**
/settings - Cài đặt bot
/clearsymbol - Xóa symbol khỏi danh sách loại trừ

**🎮 Điều khiển:**
/pause - Tạm dừng bot
/resume - Tiếp tục bot
/updatesymbols [số lượng] - Cập nhật symbols

**❓ Hỗ trợ:**
/help - Hiển thị menu này
"""
        return help_text

    def _handle_keyboard(self, _user_id: str, _args: str, _message) -> str:
        return "📱 **Menu lệnh nhanh:**\n\n" + self._handle_help(_user_id, _args, _message)

    def send_main_keyboard(self):
        keyboard_message = self._handle_keyboard("", "", None)
        if self.bot.is_ready() and hasattr(self.bot, 'loop') and self.bot.loop:
            try:
                future = asyncio.run_coroutine_threadsafe(self.send_message(keyboard_message), self.bot.loop)
                future.result(timeout=10)
            except Exception as e:
                logger.error(f"Lỗi gửi keyboard message: {e}")

    def wait_for_ready(self, timeout: int = 30) -> bool:
        """Đợi Discord bot sẵn sàng"""
        import time
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.bot.is_ready() and hasattr(self.bot, 'loop') and self.bot.loop and self.channel:
                return True
            time.sleep(0.5)
        return False

    def register_trading_commands(self, callbacks):
        """Đăng ký các lệnh trading và tạo slash commands tương ứng"""
        self.trading_callbacks = callbacks

        # Đăng ký các lệnh cũ (để tương thích)
        if 'status' in callbacks:
            self.register_command('status',
                lambda user_id, args, msg: self.handle_status(user_id, args, msg, callbacks['status']))

        if 'balance' in callbacks:
            self.register_command('balance',
                lambda user_id, args, msg: self.handle_balance(user_id, args, msg, callbacks['balance']))

        if 'stats' in callbacks:
            self.register_command('stats',
                lambda user_id, args, msg: self.handle_stats(user_id, args, msg, callbacks['stats']))

        if 'orders' in callbacks:
            self.register_command('orders',
                lambda user_id, args, msg: self.handle_orders(user_id, args, msg, callbacks['orders']))

        if 'watchlist' in callbacks:
            self.register_command('watchlist',
                lambda user_id, args, msg: self.handle_watchlist(user_id, args, msg, callbacks['watchlist']))

        if 'openpositions' in callbacks:
            self.register_command('openpositions',
                lambda user_id, args, msg: self.handle_openpositions(user_id, args, msg, callbacks['openpositions']))

        if 'settings' in callbacks:
            self.register_command('settings',
                lambda user_id, args, msg: self.handle_settings(user_id, args, msg, callbacks['settings']))

        if 'clearsymbol' in callbacks:
            self.register_command('clearsymbol',
                lambda user_id, args, msg: self.handle_clearsymbol(user_id, args, msg, callbacks['clearsymbol']))

        if 'pause' in callbacks:
            self.register_command('pause',
                lambda user_id, args, msg: self.handle_pause(user_id, args, msg, callbacks['pause']))

        if 'resume' in callbacks:
            self.register_command('resume',
                lambda user_id, args, msg: self.handle_resume(user_id, args, msg, callbacks['resume']))

        if 'updatesymbols' in callbacks:
            self.register_command('updatesymbols',
                lambda user_id, args, msg: self.handle_update_symbols(user_id, args, msg, callbacks['updatesymbols']))

        # Tạo slash commands cho trading
        self._setup_trading_slash_commands()

    def _setup_trading_slash_commands(self):
        """Thiết lập slash commands cho trading"""
        if not hasattr(self, 'trading_callbacks'):
            return

        # Status command
        if 'status' in self.trading_callbacks:
            @self.bot.tree.command(name="status", description="Xem trạng thái bot")
            async def status_command(interaction: discord.Interaction):
                if interaction.user.id != self.admin_id:
                    await interaction.response.send_message("❌ Bạn không có quyền sử dụng lệnh này.", ephemeral=True)
                    return
                try:
                    result = self.trading_callbacks['status']()
                    await interaction.response.send_message(result)
                except Exception as e:
                    await interaction.response.send_message(f"⚠️ Lỗi: {str(e)}", ephemeral=True)

        # Balance command
        if 'balance' in self.trading_callbacks:
            @self.bot.tree.command(name="balance", description="Xem số dư tài khoản")
            async def balance_command(interaction: discord.Interaction):
                if interaction.user.id != self.admin_id:
                    await interaction.response.send_message("❌ Bạn không có quyền sử dụng lệnh này.", ephemeral=True)
                    return
                try:
                    result = self.trading_callbacks['balance']()
                    await interaction.response.send_message(result)
                except Exception as e:
                    await interaction.response.send_message(f"⚠️ Lỗi: {str(e)}", ephemeral=True)

        # Pause command
        if 'pause' in self.trading_callbacks:
            @self.bot.tree.command(name="pause", description="Tạm dừng bot")
            async def pause_command(interaction: discord.Interaction):
                if interaction.user.id != self.admin_id:
                    await interaction.response.send_message("❌ Bạn không có quyền sử dụng lệnh này.", ephemeral=True)
                    return
                try:
                    result = self.trading_callbacks['pause']()
                    await interaction.response.send_message(result)
                except Exception as e:
                    await interaction.response.send_message(f"⚠️ Lỗi: {str(e)}", ephemeral=True)

        # Resume command
        if 'resume' in self.trading_callbacks:
            @self.bot.tree.command(name="resume", description="Tiếp tục bot")
            async def resume_command(interaction: discord.Interaction):
                if interaction.user.id != self.admin_id:
                    await interaction.response.send_message("❌ Bạn không có quyền sử dụng lệnh này.", ephemeral=True)
                    return
                try:
                    result = self.trading_callbacks['resume']()
                    await interaction.response.send_message(result)
                except Exception as e:
                    await interaction.response.send_message(f"⚠️ Lỗi: {str(e)}", ephemeral=True)

        # Dashboard command
        if 'dashboard' in self.trading_callbacks:
            @self.bot.tree.command(name="dashboard", description="Khởi động dashboard tự động cập nhật")
            async def dashboard_command(interaction: discord.Interaction):
                if interaction.user.id != self.admin_id:
                    await interaction.response.send_message("❌ Bạn không có quyền sử dụng lệnh này.", ephemeral=True)
                    return
                try:
                    result = self.trading_callbacks['dashboard']()
                    await interaction.response.send_message(result)
                except Exception as e:
                    await interaction.response.send_message(f"⚠️ Lỗi: {str(e)}", ephemeral=True)

    def handle_status(self, _user_id: str, _args: str, _message, callback) -> str:
        try:
            return callback()
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh status: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_balance(self, _user_id: str, _args: str, _message, callback) -> str:
        try:
            return callback()
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh balance: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_stats(self, _user_id: str, _args: str, _message, callback) -> str:
        try:
            return callback()
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh stats: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_orders(self, _user_id: str, _args: str, _message, callback) -> str:
        try:
            return callback()
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh orders: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_watchlist(self, _user_id: str, _args: str, _message, callback) -> str:
        try:
            return callback()
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh watchlist: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_openpositions(self, _user_id: str, _args: str, _message, callback) -> str:
        try:
            return callback()
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh openpositions: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_settings(self, _user_id: str, args: str, _message, callback) -> str:
        try:
            return callback(args)
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh settings: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_clearsymbol(self, _user_id: str, args: str, _message, callback) -> str:
        try:
            return callback(args)
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh clearsymbol: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_pause(self, _user_id: str, _args: str, _message, callback) -> str:
        try:
            return callback()
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh pause: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_resume(self, _user_id: str, _args: str, _message, callback) -> str:
        try:
            return callback()
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh resume: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_update_symbols(self, _user_id: str, args: str, _message, callback) -> str:
        try:
            count = 20
            if args.strip():
                try:
                    count = int(args.strip())
                except ValueError:
                    return "⚠️ Số lượng symbol không hợp lệ. Sử dụng: /updatesymbols [số]"
            return callback(count)
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh updatesymbols: {e}")
            return f"⚠️ Lỗi: {str(e)}"
