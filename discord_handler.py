import discord
from discord.ext import commands
import asyncio
import threading
import logging
import time

logger = logging.getLogger('discord')

class DiscordHandler:
    def __init__(self, token: str, admin_id: str, guild_id: str, channel_name: str = "mexc-auto"):
        self.token = token
        self.admin_id = int(admin_id)
        self.guild_id = int(guild_id)
        self.channel_name = channel_name
        self.channel = None
        self.bot_thread = None
        self.trading_callbacks = {}
        
        intents = discord.Intents.default()
        intents.message_content = False
        self.bot = commands.Bot(command_prefix='/', intents=intents)
        
        self._setup_events()
        logger.info(f"DiscordHandler khởi tạo cho Guild {guild_id}, Channel {channel_name}")

    def _setup_events(self):
        @self.bot.event
        async def on_ready():
            logger.info(f'Discord bot đã kết nối: {self.bot.user}')
            guild = self.bot.get_guild(self.guild_id)
            if guild:
                self.channel = discord.utils.get(guild.channels, name=self.channel_name)
                if self.channel:
                    logger.info(f'Đã tìm thấy channel: {self.channel_name}')
                    try:
                        synced = await self.bot.tree.sync()
                        logger.info(f"Đã sync {len(synced)} slash commands")
                    except Exception as e:
                        logger.error(f"Lỗi sync slash commands: {e}")
                else:
                    logger.error(f'Không tìm thấy channel: {self.channel_name}')
            else:
                logger.error(f'Không tìm thấy guild với ID: {self.guild_id}')

        # Tạo slash commands đơn giản
        @self.bot.tree.command(name="start", description="Khởi động bot")
        async def start_cmd(interaction: discord.Interaction):
            if interaction.user.id != self.admin_id:
                await interaction.response.send_message("❌ Bạn không có quyền sử dụng lệnh này.", ephemeral=True)
                return
            await interaction.response.send_message("🤖 **Trading Bot Discord**\n\nBot đã sẵn sàng hoạt động!\nSử dụng /help để xem danh sách lệnh.")

        @self.bot.tree.command(name="help", description="Hiển thị danh sách lệnh")
        async def help_cmd(interaction: discord.Interaction):
            if interaction.user.id != self.admin_id:
                await interaction.response.send_message("❌ Bạn không có quyền sử dụng lệnh này.", ephemeral=True)
                return
            help_text = """🤖 **DANH SÁCH LỆNH BOT**

**📊 Thông tin:**
/status - Trạng thái bot
/balance - Số dư tài khoản
/orders - Lệnh đang mở
/dashboard - Khởi động dashboard
/stats - Thống kê giao dịch

**🎮 Điều khiển:**
/pause - Tạm dừng bot
/resume - Tiếp tục bot

**❓ Hỗ trợ:**
/help - Hiển thị menu này"""
            await interaction.response.send_message(help_text)

        @self.bot.tree.command(name="status", description="Xem trạng thái bot")
        async def status_cmd(interaction: discord.Interaction):
            if interaction.user.id != self.admin_id:
                await interaction.response.send_message("❌ Bạn không có quyền sử dụng lệnh này.", ephemeral=True)
                return
            try:
                if 'status' in self.trading_callbacks:
                    result = self.trading_callbacks['status']()
                    await interaction.response.send_message(result)
                else:
                    await interaction.response.send_message("⚠️ Lệnh chưa được kích hoạt.", ephemeral=True)
            except Exception as e:
                await interaction.response.send_message(f"⚠️ Lỗi: {str(e)}", ephemeral=True)

        @self.bot.tree.command(name="balance", description="Xem số dư tài khoản")
        async def balance_cmd(interaction: discord.Interaction):
            if interaction.user.id != self.admin_id:
                await interaction.response.send_message("❌ Bạn không có quyền sử dụng lệnh này.", ephemeral=True)
                return
            try:
                if 'balance' in self.trading_callbacks:
                    result = self.trading_callbacks['balance']()
                    await interaction.response.send_message(result)
                else:
                    await interaction.response.send_message("⚠️ Lệnh chưa được kích hoạt.", ephemeral=True)
            except Exception as e:
                await interaction.response.send_message(f"⚠️ Lỗi: {str(e)}", ephemeral=True)

        @self.bot.tree.command(name="dashboard", description="Khởi động dashboard")
        async def dashboard_cmd(interaction: discord.Interaction):
            if interaction.user.id != self.admin_id:
                await interaction.response.send_message("❌ Bạn không có quyền sử dụng lệnh này.", ephemeral=True)
                return
            try:
                if 'dashboard' in self.trading_callbacks:
                    result = self.trading_callbacks['dashboard']()
                    await interaction.response.send_message(result)
                else:
                    await interaction.response.send_message("⚠️ Lệnh chưa được kích hoạt.", ephemeral=True)
            except Exception as e:
                await interaction.response.send_message(f"⚠️ Lỗi: {str(e)}", ephemeral=True)

        @self.bot.tree.command(name="orders", description="Xem lệnh đang mở")
        async def orders_cmd(interaction: discord.Interaction):
            if interaction.user.id != self.admin_id:
                await interaction.response.send_message("❌ Bạn không có quyền sử dụng lệnh này.", ephemeral=True)
                return
            try:
                if 'orders' in self.trading_callbacks:
                    result = self.trading_callbacks['orders']()
                    await interaction.response.send_message(result)
                else:
                    await interaction.response.send_message("⚠️ Lệnh chưa được kích hoạt.", ephemeral=True)
            except Exception as e:
                await interaction.response.send_message(f"⚠️ Lỗi: {str(e)}", ephemeral=True)

        @self.bot.tree.command(name="pause", description="Tạm dừng bot")
        async def pause_cmd(interaction: discord.Interaction):
            if interaction.user.id != self.admin_id:
                await interaction.response.send_message("❌ Bạn không có quyền sử dụng lệnh này.", ephemeral=True)
                return
            try:
                if 'pause' in self.trading_callbacks:
                    result = self.trading_callbacks['pause']()
                    await interaction.response.send_message(result)
                else:
                    await interaction.response.send_message("⚠️ Lệnh chưa được kích hoạt.", ephemeral=True)
            except Exception as e:
                await interaction.response.send_message(f"⚠️ Lỗi: {str(e)}", ephemeral=True)

        @self.bot.tree.command(name="resume", description="Tiếp tục bot")
        async def resume_cmd(interaction: discord.Interaction):
            if interaction.user.id != self.admin_id:
                await interaction.response.send_message("❌ Bạn không có quyền sử dụng lệnh này.", ephemeral=True)
                return
            try:
                if 'resume' in self.trading_callbacks:
                    result = self.trading_callbacks['resume']()
                    await interaction.response.send_message(result)
                else:
                    await interaction.response.send_message("⚠️ Lệnh chưa được kích hoạt.", ephemeral=True)
            except Exception as e:
                await interaction.response.send_message(f"⚠️ Lỗi: {str(e)}", ephemeral=True)

        @self.bot.tree.command(name="stats", description="Xem thống kê giao dịch")
        async def stats_cmd(interaction: discord.Interaction):
            if interaction.user.id != self.admin_id:
                await interaction.response.send_message("❌ Bạn không có quyền sử dụng lệnh này.", ephemeral=True)
                return
            try:
                if 'stats' in self.trading_callbacks:
                    result = self.trading_callbacks['stats']()
                    await interaction.response.send_message(result)
                else:
                    await interaction.response.send_message("⚠️ Lệnh chưa được kích hoạt.", ephemeral=True)
            except Exception as e:
                await interaction.response.send_message(f"⚠️ Lỗi: {str(e)}", ephemeral=True)

    def register_trading_commands(self, callbacks):
        self.trading_callbacks = callbacks
        logger.info(f"Đã đăng ký {len(callbacks)} trading commands")

    async def send_message(self, message: str) -> bool:
        if not self.channel:
            logger.warning("Channel chưa được thiết lập")
            return False
        
        if len(message) > 2000:
            message = message[:1997] + "..."
        
        try:
            await self.channel.send(message)
            return True
        except Exception as e:
            logger.error(f"Lỗi gửi tin nhắn Discord: {e}")
            return False

    def send_message_sync(self, message: str) -> bool:
        if not self.bot.is_ready():
            logger.warning("Bot chưa sẵn sàng")
            return False

        try:
            future = asyncio.run_coroutine_threadsafe(self.send_message(message), self.bot.loop)
            return future.result(timeout=10)
        except Exception as e:
            logger.error(f"Lỗi gửi tin nhắn sync: {e}")
            return False

    async def send_message_and_get_id(self, message: str):
        if not self.channel:
            return None
        
        if len(message) > 2000:
            message = message[:1997] + "..."
        
        try:
            msg = await self.channel.send(message)
            return msg.id
        except Exception as e:
            logger.error(f"Lỗi gửi tin nhắn: {e}")
            return None

    def send_message_and_get_id_sync(self, message: str):
        if not self.bot.is_ready():
            return None

        try:
            future = asyncio.run_coroutine_threadsafe(self.send_message_and_get_id(message), self.bot.loop)
            return future.result(timeout=10)
        except Exception as e:
            logger.error(f"Lỗi gửi tin nhắn sync: {e}")
            return None

    async def edit_message(self, message_id: int, new_content: str) -> bool:
        if not self.channel:
            return False
        
        try:
            message = await self.channel.fetch_message(message_id)
            await message.edit(content=new_content)
            return True
        except Exception as e:
            logger.error(f"Lỗi edit tin nhắn: {e}")
            return False

    def edit_message_sync(self, message_id: int, new_content: str) -> bool:
        if not self.bot.is_ready():
            return False

        try:
            future = asyncio.run_coroutine_threadsafe(self.edit_message(message_id, new_content), self.bot.loop)
            return future.result(timeout=10)
        except Exception as e:
            logger.error(f"Lỗi edit tin nhắn sync: {e}")
            return False

    def start_polling(self):
        if self.bot_thread and self.bot_thread.is_alive():
            logger.warning("Discord bot đã đang chạy")
            return
        
        self.bot_thread = threading.Thread(target=self._run_bot, daemon=True)
        self.bot_thread.start()
        logger.info("Đã bắt đầu Discord bot")

    def _run_bot(self):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            logger.info("Bắt đầu kết nối Discord bot...")
            loop.run_until_complete(self.bot.start(self.token))
        except Exception as e:
            logger.error(f"Lỗi chạy Discord bot: {e}")
        finally:
            if not loop.is_closed():
                loop.close()

    def stop_polling_updates(self):
        if self.bot_thread and self.bot_thread.is_alive():
            try:
                if hasattr(self.bot, 'loop') and self.bot.loop and not self.bot.loop.is_closed():
                    asyncio.run_coroutine_threadsafe(self.bot.close(), self.bot.loop)
                self.bot_thread.join(timeout=5)
            except Exception as e:
                logger.error(f"Lỗi khi dừng Discord bot: {e}")
            logger.info("Đã dừng Discord bot")

    def wait_for_ready(self, timeout: int = 30) -> bool:
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.bot.is_ready() and self.channel:
                return True
            time.sleep(0.5)
        return False

    def send_main_keyboard(self):
        keyboard_message = """📱 **Menu lệnh nhanh:**

🤖 **DANH SÁCH LỆNH BOT**

**📊 Thông tin:**
!status - Trạng thái bot
!balance - Số dư tài khoản
!orders - Lệnh đang mở
!dashboard - Khởi động dashboard
!stats - Thống kê giao dịch

**🎮 Điều khiển:**
!pause - Tạm dừng bot
!resume - Tiếp tục bot

**❓ Hỗ trợ:**
!help - Hiển thị menu này"""
        
        if self.bot.is_ready():
            try:
                future = asyncio.run_coroutine_threadsafe(self.send_message(keyboard_message), self.bot.loop)
                future.result(timeout=10)
            except Exception as e:
                logger.error(f"Lỗi gửi keyboard message: {e}")
